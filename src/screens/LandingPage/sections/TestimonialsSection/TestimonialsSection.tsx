import {
  AwardIcon,
  BarChartIcon,
  BriefcaseIcon,
  GraduationCapIcon,
  UsersIcon,
} from "lucide-react";

import { Card, CardContent } from "../../../../components/ui/card";

export const TestimonialsSection = (): JSX.Element => {
  // Service card data for mapping
  const serviceCards = [
    {
      id: 1,
      title: "Career ready curriculum",
      description:
        "Gain industry-aligned knowledge that prepares you for real-world success.",
      bgColor: "bg-white",
      textColor: "text-[#0b1131]",
      descriptionOpacity: "opacity-50",
      icon: <img src="/public/card-1.png" alt="" />,
      bgImage: <img src="/public/bg-card-1.png" alt="" />,
    },
    {
      id: 2,
      title: "Interview Training",
      description:
        "Master the art of interviews with expert-led coaching and mock sessions.",
      bgColor: "bg-[#3f43a9]",
      textColor: "text-white",
      descriptionOpacity: "opacity-50",
      icon: <img src="/public/card-2.svg" alt="" />,
      bgImage: <img src="/public/bg-card-2.png" alt="" />
    },
    {
      id: 3,
      title: "Global Certifications",
      description:
        "Boost your credentials with internationally recognized certifications.",
      bgColor: "bg-[#101478]",
      textColor: "text-white",
      descriptionOpacity: "opacity-50",
      icon: <img src="/public/card-3.svg" alt="" />,
      bgImage: <img src="/public/bg-card-3.png" alt="" />
    },
    {
      id: 4,
      title: "Employability Skills",
      description:
        "Develop communication, teamwork, leadership, and more for workplace excellence.",
      bgColor: "bg-[#3f43a9]",
      textColor: "text-white",
      descriptionOpacity: "opacity-50",
      icon: <img src="/public/card-4.svg" alt="" />,
      bgImage: <img src="/public/bg-card-4.png" alt="" />
    },
    {
      id: 5,
      title: "100% Placement Opportunities",
      description:
        "Unlock top job opportunities with guaranteed placement support.",
      bgColor: "bg-[#101478]",
      textColor: "text-white",
      descriptionOpacity: "opacity-50",
      icon: <img src="/public/card-5.svg" alt="" />,
      bgImage: <img src="/public/bg-card-5.png" alt="" />
    },
  ];

  return (
    <section className="w-full py-16 bg-white bg-opacity-20 relative">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h3 className="font-semibold text-2xl text-[#151bb1] font-['Poppins',Helvetica] mb-4">
            Our Services
          </h3>
          <h2 className="font-bold text-5xl text-[#0b1131] font-['Poppins',Helvetica] leading-[68px]">
            DISCOVER THE MAGNATE MAGIC
          </h2>
        </div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
          {serviceCards.slice(0, 3).map((card) => (
            <div key={card.id} className="relative">
              <Card
                className={`${card.bgColor} rounded-lg shadow-[0px_10px_100px_10px_#0000001a] h-[444px] relative overflow-hidden`}
              >
                <div className="relative w-full h-full">
                  {/* Background Image */}
                  <img
                    className="absolute inset-0 w-full h-full object-cover"
                    alt={`${card.title} background`}
                    src={${card.bgImage}}
                    // style={{ marginLeft: 10, marginRight: 10 }}
                  />

                  {/* Card Content */}
                  <CardContent className="relative z-10 flex flex-col items-center justify-center h-full pt-16">
                    <div className="flex justify-center mb-8">{card.icon}</div>
                    <h3
                      className={`${card.textColor} font-bold text-2xl text-center font-['Poppins',Helvetica] mb-6 px-4`}
                    >
                      {card.title}
                    </h3>
                    <p
                      className={`${card.textColor} ${card.descriptionOpacity} font-medium text-sm text-center font-['Poppins',Helvetica] leading-7 max-w-[293px]`}
                    >
                      {card.description}
                    </p>
                  </CardContent>
                </div>
              </Card>
            </div>
          ))}
        </div>

        {/* Bottom Row */}
        {/* Bottom Row */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {serviceCards.slice(3, 5).map((card) => (
            <div key={card.id} className="relative">
              <Card
                className={`${card.bgColor} rounded-lg shadow-[0px_10px_100px_10px_#0000001a] h-[444px] relative overflow-hidden`}
              >
                <div className="relative w-full h-full">
                  {/* Background Image */}
                  <img
                    className="absolute inset-0 w-full h-full object-cover"
                    alt={`${card.title} background`}
                    src={card.bgImage}
                  />

                  {/* Card Content */}
                  <CardContent className="relative z-10 flex flex-col items-center justify-center h-full pt-16">
                    <div className="flex justify-center mb-8">{card.icon}</div>
                    <h3
                      className={`${card.textColor} font-bold text-2xl text-center font-['Poppins',Helvetica] mb-6 px-4`}
                    >
                      {card.title}
                    </h3>
                    <p
                      className={`${card.textColor} ${card.descriptionOpacity} font-medium text-sm text-center font-['Poppins',Helvetica] leading-7 max-w-[293px]`}
                    >
                      {card.description}
                    </p>
                  </CardContent>
                </div>
              </Card>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};
