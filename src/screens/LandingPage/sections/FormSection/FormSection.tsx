import React from "react";
import { But<PERSON> } from "../../../../components/ui/button";
import { Card, CardContent } from "../../../../components/ui/card";
import { Checkbox } from "../../../../components/ui/checkbox";
import { Input } from "../../../../components/ui/input";
import { Textarea } from "../../../../components/ui/textarea";

export const FormSection = (): JSX.Element => {
  return (
    <section className="w-full bg-white py-16">
      <div className="container mx-auto px-4 max-w-[1296px]">
        <div className="flex flex-col md:flex-row gap-12">
          {/* Left side with cards */}
          <div className="w-full md:w-1/2 flex flex-col gap-8">
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card className="rounded-lg  p-6 flex flex-col items-center w-[100%]">
                <img src="/public/form-card-1.svg" alt="" />              
              </Card>

              <Card className=" rounded-lg  flex flex-col items-center">
                <img src="/public/form-card-2.svg" alt="" />

              </Card>

              <Card className="bg-white rounded-lg flex flex-col items-center md:col-span-2 md:mx-auto md:w-1/2">
                <img src="form-card-3.svg" alt="" />
              </Card>
            </div>
          </div>

          {/* Right side with form */}
          <div className="w-full md:w-1/2">
          <div className="text-center md:text-right mb-8">
              <h3 className="text-[#151bb1] font-semibold text-2xl mb-2">Contact Us</h3><br />
              <h2 className="text-[#0b1131] font-bold text-5xl mb-4">Get in touch</h2><br />
              <p className="text-[#a0abb8] font-medium text-sm">
                Feel free to contact us at any time, we will get back to you as
                soon as possible. Get started in just 30 seconds.
              </p>
            <div className="bg-white rounded-lg shadow-lg p-6">
              <form className="flex flex-col gap-6">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-[#0f001a] mb-1">
                    Name<span className="text-red-500">*</span>
                  </label>
                  <Input 
                    id="name" 
                    placeholder="First_Name Last_Name" 
                    className="w-full border-[#acb5c1] rounded-lg p-3" 
                  />
                </div>
                
                <div>
                  <label htmlFor="age" className="block text-sm font-medium text-[#0f001a] mb-1">
                    Age<span className="text-red-500">*</span>
                  </label>
                  <Input 
                    id="age" 
                    placeholder="" 
                    className="w-full border-[#acb5c1] rounded-lg p-3" 
                  />
                </div>
                
                <div>
                  <label htmlFor="qualifications" className="block text-sm font-medium text-[#0f001a] mb-1">
                    Qualifications<span className="text-red-500">*</span>
                  </label>
                  <Input 
                    id="qualifications" 
                    placeholder="" 
                    className="w-full border-[#acb5c1] rounded-lg p-3" 
                  />
                </div>
                
                <div>
                  <label htmlFor="phone" className="block text-sm font-medium text-[#0f001a] mb-1">
                    Phone<span className="text-red-500">*</span>
                  </label>
                  <Input 
                    id="phone" 
                    placeholder="" 
                    className="w-full border-[#acb5c1] rounded-lg p-3" 
                  />
                </div>
                
                <div>
                  <label htmlFor="message" className="block text-sm font-medium text-[#0f001a] mb-1">
                    Your Message
                  </label>
                  <Textarea 
                    id="message" 
                    placeholder="" 
                    className="w-full border-[#acb5c1] rounded-lg p-3 min-h-[126px]" 
                  />
                </div>
                
                <div className="flex items-center gap-2">
                  <Checkbox
                    id="demo"
                    className="w-5 h-5 rounded border border-solid border-[#a0abb8]"
                  />
                  <label
                    htmlFor="demo"
                    className="text-[#0f001a] font-medium text-sm"
                  >
                    FREE Demo
                  </label>
                </div>
                
                <Button className="w-full py-3 bg-[#ffc350] hover:bg-[#ffc350]/90 rounded text-[#0f1377] font-semibold">
                  Submit and unlock a free Career consultation
                </Button>
              </form>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
